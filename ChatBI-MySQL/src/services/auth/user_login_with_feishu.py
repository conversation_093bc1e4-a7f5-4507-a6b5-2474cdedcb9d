import os
import requests
from datetime import datetime,timedelta
import urllib.parse
from flask import redirect, make_response, session, request, render_template

from src.services.xianmudb.query_service import execute_business_query
from src.utils.logger import logger
from utils.user_utils import get_api_token
from src.utils.in_memory_cache import in_memory_cache # 导入缓存装饰器

# 飞书应用的 App ID 和 App Secret
APP_ID = os.getenv("FEISHU_APP_ID")
APP_SECRET = os.getenv("FEISHU_APP_SECRET")

if not APP_ID or not APP_SECRET:
    raise ValueError("飞书应用的 App ID 和 App Secret 未配置")

# 本地调试：http://127.0.0.1:5700
HOST_NAME = os.getenv(
    "CHAT_BI_HOST_NAME", "https://chat-bi.summerfarm.net"
)
MINUTES_TO_FORCE_REFRESH_TOKEN = int(os.getenv("MINUTES_TO_FORCE_REFRESH_TOKEN", 10))

logger.info(f"HOST_NAME: {HOST_NAME}")

@in_memory_cache(expire_seconds=1200) # 应用缓存装饰器，缓存1200秒
def get_user_admin_id(user_name, email):
    # SQL 查询语句，优先匹配 email，然后匹配 realname
    sql = f"""
SELECT
  `admin_id`,`realname`,`username`
FROM
  `admin`
WHERE
  `is_disabled` = 0
  AND (username = '{email}' OR realname = '{user_name}')
ORDER BY
  CASE WHEN username = '{email}' THEN 0 ELSE 1 END -- 如果 username 匹配 email，则优先。否则，优先级较低
LIMIT 1;
    """
    result = execute_business_query(sql)
    # 检查结果是否有效并包含数据
    if result and result.data and len(result.data) > 0:
        # 确保至少有一行数据
        logger.info(f"User {user_name} ({email}) found in database:{result}")
        # 返回第一行的第一个字段值（admin_id）
        return result.data[0][0]
    # 如果没有数据或结果无效，返回None
    logger.error(f"User {user_name} ({email}) not found in database.")
    return None


scope = [
    "offline_access",
    "contact:user.email:readonly",
    "contact:department.base:readonly",
    "contact:user.base:readonly",
    "contact:user.employee:readonly",
    "contact:contact.base:readonly",
    "docs:document:import",
    "drive:drive",
    "drive:file",
    "drive:file:upload",
    "base:app:create",
    "bitable:app",
    "aily:file:write",
    "wiki:wiki:readonly",
    "docx:document:readonly",
]

scope_encoded = urllib.parse.quote_plus(" ".join(scope))


def get_expiry_at():
    return (datetime.now() + timedelta(hours=2)).isoformat()


def login(destination_path:str = None):
    access_token = request.cookies.get("access_token")
    expires_at = request.cookies.get("expires_at")
    refresh_token = request.cookies.get("refresh_token")
    session_id = request.cookies.get("session_id")

    # 检查现有token是否有效
    if access_token and expires_at:
        expires_at_datetime = datetime.fromisoformat(expires_at)
        now = datetime.now()
        time_diff = (expires_at_datetime - now).total_seconds() / 60  # 剩余分钟数

        if expires_at_datetime > now and time_diff > MINUTES_TO_FORCE_REFRESH_TOKEN:
            # access token 仍然有效，且距离过期时间大于阈值，无需重新授权
            # 新增：更新 user 表中的用户信息
            try:
                user_info = session.get("user_info")
                if not user_info:
                    user_info = get_user_info(access_token)
                if user_info:
                    upsert_user_info(
                        name=user_info.get("name"),
                        email=user_info.get("email"),
                        user_id=user_info.get("user_id"),
                        job_title=user_info.get("job_title"),
                        open_id=user_info.get("open_id"),
                        last_login_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    )
                else:
                    logger.error("无法获取用户信息，未能更新 user 表。")
            except Exception as e:
                logger.error(f"更新 user 表失败: {e}", exc_info=True)
            return redirect(f"{HOST_NAME}/")
        logger.info(
            f"access token({expires_at_datetime}过期)快过期了，尝试用refresh token刷新"
        )

    # 尝试使用refresh token刷新
    if refresh_token:
        # 使用 refresh token 刷新 access token
        new_access_token, new_refresh_token = refresh_token_and_get_new_access_token(
            refresh_token=refresh_token
        )
        if new_access_token:
            # 更新 access token
            resp = make_response(redirect(f"{HOST_NAME}/"))
            # 如果是https请求，设置Secure和SameSite属性
            is_secure = request.scheme == "https"
            cookie_kwargs = {
                "secure": is_secure,
                "samesite": "None" if is_secure else None,
            }
            resp.set_cookie("access_token", new_access_token, **cookie_kwargs)
            resp.set_cookie("refresh_token", new_refresh_token, **cookie_kwargs)
            resp.set_cookie("expires_at", get_expiry_at(), **cookie_kwargs)

            # 如果有session_id，更新数据库中的access token
            if session_id:
                user_session_service.update_session_tokens(
                    session_id=session_id,
                    access_token=new_access_token,
                    refresh_token=new_refresh_token,
                    expires_at=datetime.now() + timedelta(hours=2)
                )

            return resp

    # 如果cookie中的refresh token失效，尝试从数据库session中恢复
    if session_id and not refresh_token:
        logger.info(f"Cookie中refresh token丢失，尝试从数据库session恢复: session_id={session_id}")
        recovered_refresh_token = user_session_service.recover_refresh_token(session_id)

        if recovered_refresh_token:
            logger.info("从数据库成功恢复refresh token，尝试刷新access token")
            # 使用数据库中的refresh token刷新
            new_access_token, new_refresh_token = refresh_token_and_get_new_access_token(
                refresh_token=recovered_refresh_token
            )

            if new_access_token:
                # 刷新成功，更新cookies和数据库
                resp = make_response(redirect(f"{HOST_NAME}/"))
                is_secure = request.scheme == "https"
                cookie_kwargs = {
                    "secure": is_secure,
                    "samesite": "None" if is_secure else None,
                }

                # 更新所有相关cookies
                resp.set_cookie("access_token", new_access_token, **cookie_kwargs)
                resp.set_cookie("refresh_token", new_refresh_token, **cookie_kwargs)
                resp.set_cookie("expires_at", get_expiry_at(), **cookie_kwargs)
                resp.set_cookie("session_id", session_id, **cookie_kwargs)  # 确保session_id cookie存在

                # 更新数据库中的token信息
                user_session_service.update_session_tokens(
                    session_id=session_id,
                    access_token=new_access_token,
                    refresh_token=new_refresh_token,
                    expires_at=datetime.now() + timedelta(hours=2)
                )

                logger.info("通过数据库session成功恢复用户登录状态")
                return resp
            else:
                logger.warning("数据库中的refresh token也已失效，需要重新授权")
        else:
            logger.warning(f"数据库中未找到有效的session: session_id={session_id}")

    # 如果 access token 和 refresh token 都不有效，则重新授权
    request_host = request.host_url.rstrip("/")
    redirect_uri = f"{request_host}/callback"
    # 将目标路径编码后放入 state 参数
    state_param = urllib.parse.quote_plus(destination_path if destination_path else "/")
    auth_url = f"https://accounts.feishu.cn/open-apis/authen/v1/authorize?app_id={APP_ID}&redirect_uri={redirect_uri}&response_type=code&scope={scope_encoded}&state={state_param}"
    logger.info(f"Redirecting to Feishu auth URL with state: {state_param}") # 增加日志记录 state
    return redirect(auth_url)


def callback():
    """
    处理飞书授权回调，获取access_token并获取用户信息，并重定向到原始请求路径
    """
    # 获取请求的主机URL并移除尾部斜杠
    request_host = request.host_url.rstrip("/")
    code = request.args.get("code")
    # 从 state 参数获取原始目标路径，并解码
    state = request.args.get("state")
    destination_path = urllib.parse.unquote_plus(state) if state else "/"
    logger.info(f"Callback received with state (destination_path): {destination_path}") # 增加日志记录 state

    if not code:
        logger.error("未收到授权码")
        return render_template(
            "login_redirect.html", success=False, error_message="授权失败：未收到授权码"
        )

    logger.info(f"收到飞书授权码: {code}")

    # 构建重定向URI
    redirect_uri = f"{request_host}/callback"
    logger.info(f"使用重定向URI: {redirect_uri}")

    # 构建授权URL，用于授权失败时重新授权
    auth_url = f"https://accounts.feishu.cn/open-apis/authen/v1/authorize?app_id={APP_ID}&redirect_uri={redirect_uri}&response_type=code&scope={scope_encoded}"

    try:
        # 请求access_token
        token_url = "https://open.feishu.cn/open-apis/authen/v2/oauth/token"
        payload = {
            "grant_type": "authorization_code",
            "client_id": APP_ID,
            "client_secret": APP_SECRET,
            "code": code,
            "redirect_uri": redirect_uri,
        }

        response = requests.post(token_url, json=payload, timeout=10)
        access_token_response = response.json()

        logger.info(f"飞书token响应: {access_token_response}")

        # 检查飞书接口是否返回错误
        if (
            "error" in access_token_response
            or "access_token" not in access_token_response
        ):
            error_msg = "飞书授权失败"
            logger.error(f"获取飞书access token失败: {access_token_response}")
            return render_template(
                "login_redirect.html",
                success=False,
                error_message=error_msg,
                auth_url=auth_url,
            )

        # 获取用户信息
        user_info = get_user_info(access_token_response["access_token"])

        if not user_info:
            return render_template(
                "login_redirect.html",
                success=False,
                error_message="您暂无权限访问，请联系管理员：唐鹏处理",
            )

        logger.info(f"用户信息: {user_info}")
        # 如果是https请求，设置Secure和SameSite属性
        # 对于https连接，需要设置Secure标志确保cookie只通过加密连接传输
        # 同时设置SameSite=None允许跨站请求时携带cookie，这对于第三方认证很重要
        is_secure = request.scheme == "https"
        logger.info(f"is_secure: {is_secure}")
        cookie_kwargs = {"secure": is_secure, "samesite": "None" if is_secure else None}
        # 将用户信息存储在session中，Flask会自动加密session数据
        # 在https环境下，session cookie也会继承secure属性
        session["user_info"] = user_info
        username = user_info.get(
            "name", "用户"
        )  # 从用户信息中获取用户名，如果不存在则默认为"用户"

        # 创建响应并设置cookies
        logger.info(f"Setting cookies for user: {username}")
        # 解析目标路径，检查是否有查询参数
        parsed_destination = urllib.parse.urlparse(destination_path)
        query_params = urllib.parse.parse_qs(parsed_destination.query)

        # 检查是否有chat参数（从登录URL传递过来的）
        chat_param = request.args.get('chat')

        # 如果有chat参数，添加到重定向URL中
        if chat_param:
            logger.info(f"检测到chat参数: {chat_param}")
            # 构建新的查询参数字符串
            if query_params:
                # 已有查询参数，添加chat参数
                query_params['chat'] = [chat_param]
                new_query = urllib.parse.urlencode(query_params, doseq=True)
                # 重建目标路径
                if parsed_destination.path:
                    destination_path = f"{parsed_destination.path}?{new_query}"
                else:
                    destination_path = f"/?{new_query}"
            else:
                # 没有查询参数，直接添加chat参数
                destination_path = f"{parsed_destination.path}?chat={chat_param}"

        # 确定最终重定向的 URL
        final_redirect_url = f"{request_host}{destination_path}"
        logger.info(f"Login successful, redirecting to: {final_redirect_url}") # 增加日志记录重定向 URL
        resp = make_response(
            render_template(
                "login_redirect.html",
                success=True,
                redirect_url=final_redirect_url, # 使用解码后的原始路径
                username=username,
            )
        )

        # 计算access token过期时间
        access_token_expires_at = datetime.now() + timedelta(hours=2)

        # 保存session到数据库（如果有refresh token）
        session_id = None
        if "refresh_token" in access_token_response:
            session_id = user_session_service.create_user_session(
                open_id=user_info.get("open_id"),
                refresh_token=access_token_response["refresh_token"],
                access_token=access_token_response["access_token"],
                access_token_expires_at=access_token_expires_at
            )

        # 设置cookies
        resp.set_cookie(
            "access_token", access_token_response["access_token"], **cookie_kwargs
        )
        if "refresh_token" in access_token_response:
            resp.set_cookie(
                "refresh_token", access_token_response["refresh_token"], **cookie_kwargs
            )
        resp.set_cookie("expires_at", get_expiry_at(), **cookie_kwargs)

        # 如果成功创建了session，也设置session_id cookie
        if session_id:
            resp.set_cookie("session_id", session_id, **cookie_kwargs)
            logger.info(f"用户登录成功，session_id已设置: {session_id}")

        # === 新增：写入/更新 user 表 ===
        try:
            upsert_user_info(
                name=user_info.get("name"),
                email=user_info.get("email"),
                user_id=user_info.get("user_id"),
                job_title=user_info.get("job_title"),
                open_id=user_info.get("open_id"),
                last_login_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            )
        except Exception as e:
            logger.error(f"写入user表失败: {e}", exc_info=True)
        return resp

    except requests.RequestException as e:
        logger.exception(f"请求飞书API时发生错误: {str(e)}")
        return render_template(
            "login_redirect.html",
            success=False,
            error_message=f"连接飞书服务器失败: {str(e)}",
        )
    except Exception as e:
        logger.exception(f"处理飞书回调时发生未知错误: {str(e)}")
        return render_template(
            "login_redirect.html",
            success=False,
            error_message=f"处理授权时发生错误: {str(e)}",
        )


def get_user_info(access_token):
    user_info_url = "https://open.feishu.cn/open-apis/authen/v1/user_info"
    headers = {"Authorization": f"Bearer {access_token}"}

    user_info_response = requests.get(user_info_url, headers=headers)

    if user_info_response.status_code == 200:
        user_info = user_info_response.json().get("data")
        logger.info(f"获取到的用户信息: {user_info}")
        email = user_info.get("enterprise_email", "unknown-email")
        if user_info.get("email") is None or "@" not in user_info.get(
            "email", ""
        ):  # 避免KeyError
            user_info["email"] = email

        # Fetch job title using v3/users endpoint
        open_id = user_info.get("open_id")
        admin_id = get_user_admin_id(
            user_name=user_info.get("name"), email=user_info.get("email")
        )
        union_id = user_info.get("union_id")
        if admin_id:
            logger.info(f"获取到了 admin_id:{admin_id}")
            user_info["admin_id"] = admin_id
        else:
            logger.error(f"未获取到 admin_id, user_info:{user_info}")
            return None
        if union_id:
            logger.info(f"获取到了 union_id:{union_id}")
            user_info["union_id"] = union_id
            api_token = get_api_token(union_id=union_id)
            logger.info(f"获取到了 api_token:{api_token}")
            user_info["summerfarm_api_token"] = api_token
        else:
            logger.error(f"未获取到 union_id, user_info:{user_info}")
        if open_id:
            job_title_url = f"https://open.feishu.cn/open-apis/contact/v3/users/{open_id}?department_id_type=open_department_id&user_id_type=open_id"
            logger.info(f"about to fetch his job title:{job_title_url}")
            job_title_response = requests.get(job_title_url, headers=headers)

            if job_title_response.status_code == 200:
                job_title_data = job_title_response.json()
                logger.info(f"job_title_data:{job_title_data}")
                job_title = (
                    job_title_data.get("data", {}).get("user", {}).get("job_title")
                )
                if job_title:
                    user_info["job_title"] = job_title
            else:
                logger.error(f"fetch job title error:{job_title_response.text}")

        return user_info
    else:
        return None

from functools import wraps
from flask import jsonify


def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        access_token = request.cookies.get("access_token")
        is_json_request = "application/json" in request.headers.get("Accept")
        
        # 处理未登录或token过期的情况
        if not access_token or is_token_expired():
            if is_json_request:
                return make_response(jsonify({"message": "Unauthorized"}), 403)
            
            # 尝试刷新token
            refresh_response = refresh_token_if_possible()
            if refresh_response:
                # 刷新成功，直接返回带有新cookie的响应
                return refresh_response
            
            # 构建登录重定向URL
            login_url = build_login_redirect_url()
            logger.info(f"需要登录，重定向到: {login_url}")
            return redirect(login_url)

        user_info = session.get("user_info")
        logger.info(f"user-info-in session:{user_info}")
        return f(*args, **kwargs)

    return decorated_function

def is_token_expired():
    """检查token是否过期"""
    expires_at = request.cookies.get("expires_at")
    return not expires_at or datetime.now() > datetime.fromisoformat(expires_at)

def refresh_token_if_possible():
    """尝试刷新token，成功返回响应对象，失败返回None"""
    refresh_token = request.cookies.get("refresh_token")
    session_id = request.cookies.get("session_id")

    # 如果cookie中没有refresh token，尝试从数据库session中获取
    if not refresh_token and session_id:
        logger.info(f"尝试从数据库session恢复refresh token: session_id={session_id}")
        refresh_token = user_session_service.recover_refresh_token(session_id)
        if refresh_token:
            logger.info("从数据库session成功恢复refresh token")

    if not refresh_token:
        return None

    new_access_token, new_refresh_token = refresh_token_and_get_new_access_token(refresh_token)
    if not new_access_token:
        return None

    # 设置新的cookie并返回响应对象
    resp = make_response(redirect(request.url))  # 重定向到当前请求的URL
    is_secure = request.scheme == "https"
    cookie_kwargs = {
        "secure": is_secure,
        "samesite": "None" if is_secure else None,
    }
    resp.set_cookie("access_token", new_access_token, **cookie_kwargs)
    resp.set_cookie("refresh_token", new_refresh_token, **cookie_kwargs)
    resp.set_cookie("expires_at", get_expiry_at(), **cookie_kwargs)

    # 如果有session_id，更新数据库中的token信息
    if session_id:
        resp.set_cookie("session_id", session_id, **cookie_kwargs)  # 确保session_id cookie存在
        user_session_service.update_session_tokens(
            session_id=session_id,
            access_token=new_access_token,
            refresh_token=new_refresh_token,
            expires_at=datetime.now() + timedelta(hours=2)
        )

    return resp

def build_login_redirect_url():
    """构建登录重定向URL，保留路径和查询参数"""
    # 获取原始URL（包括路径和查询参数）
    full_url = request.url
    parsed_url = urllib.parse.urlparse(full_url)
    
    # 只保留路径和查询参数部分作为next参数
    path_and_query = parsed_url.path
    if parsed_url.query:
        path_and_query += f"?{parsed_url.query}"
    next_param = urllib.parse.quote_plus(path_and_query)
    
    return f"{HOST_NAME}/login?next={next_param}"


def refresh_token_and_get_new_access_token(refresh_token: str) -> tuple[str, str]:
    # 使用refresh_token进行refresh并获取新的access_token
    token_url = "https://open.feishu.cn/open-apis/authen/v2/oauth/token"
    payload = {
        "grant_type": "refresh_token",
        "client_id": APP_ID,
        "client_secret": APP_SECRET,
        "refresh_token": refresh_token,
    }
    response = requests.post(token_url, json=payload)
    access_token = response.json()
    logger.info(f"refreshed access_token:{access_token}")
    if "access_token" not in access_token:
        return None, None
    return access_token["access_token"], access_token["refresh_token"]


# === 新增：导入 user 表相关操作 ===
from src.db.connection import execute_db_query as _execute_db_query  # Keep this for now as it's a direct DB operation


def create_user_table_if_not_exists():
    """
    尝试创建 user 表（如果不存在），用于记录登录用户信息。
    """
    sql = """
    CREATE TABLE IF NOT EXISTS `user` (
        `id` INT AUTO_INCREMENT PRIMARY KEY,
        `name` VARCHAR(64) NOT NULL,
        `email` VARCHAR(128) NOT NULL,
        `user_id` VARCHAR(64) NOT NULL,
        `job_title` VARCHAR(128),
        `open_id` VARCHAR(64) NOT NULL,
        `last_login_time` DATETIME NOT NULL,
        UNIQUE KEY `uniq_user_openid` (`open_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    """
    try:
        _execute_db_query(sql)
        logger.info("user表已存在或创建成功")
    except Exception as e:
        logger.error(f"创建user表失败: {e}", exc_info=True)





def upsert_user_info(name, email, user_id, job_title, open_id, last_login_time=None):
    """
    插入或更新 user 表中的用户信息（根据 open_id 唯一）。
    """
    if last_login_time is None:
        last_login_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    sql = """
    INSERT INTO `user` (`name`, `email`, `user_id`, `job_title`, `open_id`, `last_login_time`)
    VALUES (%s, %s, %s, %s, %s, %s)
    ON DUPLICATE KEY UPDATE
        `name`=VALUES(`name`),
        `email`=VALUES(`email`),
        `user_id`=VALUES(`user_id`),
        `job_title`=VALUES(`job_title`),
        `last_login_time`=VALUES(`last_login_time`);
    """
    params = (name, email, user_id, job_title, open_id, last_login_time)
    try:
        _execute_db_query(sql, params)
        logger.info(f"user表 upsert 成功: open_id={open_id}")
    except Exception as e:
        logger.error(f"user表 upsert 失败: {e}", exc_info=True)


# 导入DDD架构的服务
from src.services.auth.user_session_service import user_session_service
from src.repositories.chatbi.user_session import UserSessionRepository


# === 新增：在模块加载时尝试创建表（幂等） ===
create_user_table_if_not_exists()
UserSessionRepository.create_table_if_not_exists()
